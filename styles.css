/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  line-height: 1.6;
  color: #333;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 导航栏样式 */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-img {
  width: 40px;
  height: 40px;
}

.logo-text h2 {
  color: #2c5aa0;
  font-size: 24px;
  font-weight: bold;
  margin: 0;
}

.logo-text span {
  color: #666;
  font-size: 12px;
  font-style: italic;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 30px;
}

.nav-menu a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-menu a:hover {
  color: #2c5aa0;
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: #333;
  margin: 3px 0;
  transition: 0.3s;
}

/* 主页横幅 */
.hero {
  padding: 120px 0 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-content h1 {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero-content h2 {
  font-size: 2rem;
  font-weight: 300;
  margin-bottom: 30px;
  opacity: 0.9;
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.8;
  line-height: 1.8;
}

.hero-buttons {
  display: flex;
  gap: 20px;
}

.btn {
  padding: 15px 30px;
  border: none;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}

.btn-primary {
  background: #ff6b6b;
  color: white;
}

.btn-primary:hover {
  background: #ff5252;
  transform: translateY(-2px);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary:hover {
  background: white;
  color: #667eea;
}

/* 英雄区域图形 */
.hero-graphic {
  position: relative;
  height: 400px;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 0;
  left: 0;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 100px;
  right: 50px;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 50px;
  left: 100px;
  animation-delay: 4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 通用区域样式 */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 20px;
}

.section-header p {
  font-size: 1.2rem;
  color: #666;
}

/* 关于我们 */
.about {
  padding: 100px 0;
  background: #f8f9fa;
}

.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 60px;
  align-items: start;
}

.about-text h3 {
  font-size: 1.8rem;
  color: #2c5aa0;
  margin-bottom: 20px;
}

.about-text p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 30px;
  color: #555;
}

.value-list {
  list-style: none;
}

.value-list li {
  padding: 15px 0;
  border-bottom: 1px solid #eee;
  font-size: 1.1rem;
}

.value-list li:last-child {
  border-bottom: none;
}

.about-stats {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.stat-item {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stat-item h4 {
  font-size: 1.5rem;
  color: #2c5aa0;
  margin-bottom: 10px;
}

.stat-item p {
  color: #666;
}

/* 核心业务 */
.services {
  padding: 100px 0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

.service-card {
  background: white;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  text-align: center;
}

.service-card:hover {
  transform: translateY(-10px);
}

.service-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.service-card h3 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 20px;
}

.service-card p {
  color: #666;
  line-height: 1.8;
  margin-bottom: 25px;
}

.service-card ul {
  list-style: none;
  text-align: left;
}

.service-card li {
  padding: 8px 0;
  color: #555;
  position: relative;
  padding-left: 20px;
}

.service-card li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #2c5aa0;
  font-weight: bold;
}

/* 产品介绍 */
.app-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.app-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.app-text h2 {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.app-text h3 {
  font-size: 1.8rem;
  font-weight: 300;
  margin-bottom: 30px;
  opacity: 0.9;
}

.app-text p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 40px;
  opacity: 0.8;
}

.app-modules {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
}

.module-item {
  text-align: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.module-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.module-item h4 {
  font-size: 1.2rem;
  margin-bottom: 10px;
}

.module-item p {
  font-size: 0.95rem;
  opacity: 0.8;
}

/* 手机模型 */
.phone-frame {
  width: 280px;
  height: auto;
  background: #333;
  border-radius: 25px;
  padding: 8px;
  margin: 0 auto;
  position: relative;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
}

.app-screenshot {
  width: 100%;
  height: auto;
  border-radius: 20px;
  display: block;
}

/* 联系我们 */
.contact {
  padding: 100px 0;
  background: #f8f9fa;
}

.contact-content {
  display: flex;
  justify-content: center;
}

.contact-info-center {
  text-align: center;
  max-width: 800px;
}

.company-logo {
  margin-bottom: 30px;
}

.contact-logo {
  width: 80px;
  height: 80px;
}

.contact-info-center h3 {
  font-size: 2rem;
  color: #2c5aa0;
  margin-bottom: 40px;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 15px;
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  text-align: left;
}

.contact-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-details strong {
  display: block;
  color: #333;
  font-size: 1.1rem;
  margin-bottom: 5px;
}

.contact-details span {
  color: #666;
  font-size: 1rem;
  line-height: 1.4;
}

.company-mission {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 20px;
  margin-top: 20px;
}

.company-mission p {
  font-size: 1.2rem;
  margin-bottom: 10px;
  opacity: 0.9;
}

.company-mission p:last-child {
  margin-bottom: 0;
  font-weight: 600;
}

/* 页脚 */
.footer {
  background: #333;
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 20px;
  color: #2c5aa0;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 10px;
}

.footer-section ul li a {
  color: #ccc;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: white;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #555;
  color: #ccc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hamburger {
    display: flex;
  }

  .nav-menu {
    display: none;
  }

  .hero-container,
  .about-content,
  .app-content,
  .contact-content,
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .app-modules {
    grid-template-columns: 1fr;
  }

  .contact-grid {
    grid-template-columns: 1fr;
  }

  .phone-frame {
    width: 220px;
  }
}
