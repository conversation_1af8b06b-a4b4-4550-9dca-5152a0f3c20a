#!/usr/bin/env python3
"""
Logo矢量化工具 - 改进版
将logo.jpg和logo1.jpg转换为高质量SVG格式
"""

import cv2
import numpy as np
from PIL import Image
import os
import sys

def preprocess_image_for_vectorization(image_path):
    """改进的图像预处理，专门用于矢量化"""
    print(f"正在预处理图像: {image_path}")

    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"错误: 无法读取图像 {image_path}")
        return None, None

    # 获取原始尺寸
    height, width = img.shape[:2]

    # 如果图像太大，先缩放到合适大小
    max_size = 800
    if max(height, width) > max_size:
        scale = max_size / max(height, width)
        new_width = int(width * scale)
        new_height = int(height * scale)
        img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_AREA)
        height, width = new_height, new_width

    # 转换为灰度图
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 使用双边滤波保持边缘的同时减少噪声
    filtered = cv2.bilateralFilter(gray, 9, 75, 75)

    # 使用Otsu阈值自动确定最佳阈值
    _, thresh = cv2.threshold(filtered, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    # 形态学操作，清理图像
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
    cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)

    return cleaned, img

def create_optimized_svg(processed_img, original_img, output_path):
    """创建优化的SVG，使用更好的轮廓检测和路径生成"""
    print(f"正在生成优化SVG: {output_path}")

    # 查找轮廓，使用层次结构
    contours, hierarchy = cv2.findContours(processed_img, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

    # 获取图像尺寸
    height, width = processed_img.shape

    # 过滤和排序轮廓
    valid_contours = []
    for i, contour in enumerate(contours):
        area = cv2.contourArea(contour)
        # 调整最小面积阈值
        min_area = max(50, (width * height) * 0.0001)  # 动态调整最小面积
        if area > min_area:
            valid_contours.append((contour, area))

    # 按面积排序，大的在前
    valid_contours.sort(key=lambda x: x[1], reverse=True)

    # 开始创建SVG内容
    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="{width}" height="{height}" viewBox="0 0 {width} {height}">
  <defs>
    <style>
      .logo-path {{
        fill: #000000;
        stroke: none;
        fill-rule: evenodd;
      }}
    </style>
  </defs>
  <rect width="{width}" height="{height}" fill="white"/>
'''

    # 处理有效轮廓
    for i, (contour, area) in enumerate(valid_contours[:20]):  # 限制最多20个轮廓
        # 使用更精确的轮廓近似
        epsilon = 0.005 * cv2.arcLength(contour, True)  # 更精确的近似
        approx = cv2.approxPolyDP(contour, epsilon, True)

        if len(approx) >= 3:  # 至少需要3个点形成一个形状
            # 创建路径字符串
            path_data = f"M {approx[0][0][0]} {approx[0][0][1]}"

            # 添加其他点
            for j in range(1, len(approx)):
                point = approx[j][0]
                path_data += f" L {point[0]} {point[1]}"

            # 闭合路径
            path_data += " Z"

            # 添加到SVG
            svg_content += f'  <path class="logo-path" d="{path_data}"/>\n'

    svg_content += '</svg>'

    # 保存SVG文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(svg_content)

    print(f"优化SVG文件已保存: {output_path}")

def create_color_svg_with_posterization(image_path, output_path):
    """创建彩色SVG，使用色调分离技术"""
    print(f"正在创建彩色SVG: {output_path}")

    # 读取原始图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"错误: 无法读取图像 {image_path}")
        return

    height, width = img.shape[:2]

    # 如果图像太大，先缩放
    max_size = 600
    if max(height, width) > max_size:
        scale = max_size / max(height, width)
        new_width = int(width * scale)
        new_height = int(height * scale)
        img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_AREA)
        height, width = new_height, new_width

    # 转换颜色空间
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

    # 应用双边滤波平滑图像但保持边缘
    smooth = cv2.bilateralFilter(img_rgb, 15, 80, 80)

    # 减少颜色数量（量化）
    data = smooth.reshape((-1, 3))
    data = np.float32(data)

    # 使用K-means聚类减少颜色
    criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 20, 1.0)
    k = 6  # 减少到6种主要颜色，避免过于复杂
    _, labels, centers = cv2.kmeans(data, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)

    # 转换回uint8
    centers = np.uint8(centers)

    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="{width}" height="{height}" viewBox="0 0 {width} {height}">
  <defs>
    <style>
      .color-path {{ fill-rule: evenodd; }}
    </style>
  </defs>
'''

    # 按颜色亮度排序，从暗到亮
    color_brightness = [(i, np.mean(color)) for i, color in enumerate(centers)]
    color_brightness.sort(key=lambda x: x[1])

    # 为每种颜色创建路径
    for brightness_idx, (color_idx, _) in enumerate(color_brightness):
        color = centers[color_idx]

        # 创建该颜色的掩码
        mask = (labels.flatten() == color_idx).reshape((height, width)).astype(np.uint8) * 255

        # 形态学操作清理掩码
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)

        # 查找该颜色区域的轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 转换颜色为十六进制
        hex_color = f"#{color[0]:02x}{color[1]:02x}{color[2]:02x}"

        # 过滤和处理轮廓
        valid_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            min_area = max(30, (width * height) * 0.0005)  # 动态最小面积
            if area > min_area:
                valid_contours.append((contour, area))

        # 按面积排序
        valid_contours.sort(key=lambda x: x[1], reverse=True)

        # 处理前10个最大的轮廓
        for contour, area in valid_contours[:10]:
            # 简化轮廓
            epsilon = 0.008 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)

            if len(approx) >= 3:
                # 创建路径
                path_data = f"M {approx[0][0][0]} {approx[0][0][1]}"
                for j in range(1, len(approx)):
                    point = approx[j][0]
                    path_data += f" L {point[0]} {point[1]}"
                path_data += " Z"

                svg_content += f'  <path class="color-path" fill="{hex_color}" d="{path_data}"/>\n'

    svg_content += '</svg>'

    # 保存SVG文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(svg_content)

    print(f"彩色SVG文件已保存: {output_path}")

def create_trace_svg(image_path, output_path):
    """创建描边风格的SVG"""
    print(f"正在创建描边SVG: {output_path}")

    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"错误: 无法读取图像 {image_path}")
        return

    height, width = img.shape[:2]

    # 缩放图像
    max_size = 500
    if max(height, width) > max_size:
        scale = max_size / max(height, width)
        new_width = int(width * scale)
        new_height = int(height * scale)
        img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_AREA)
        height, width = new_height, new_width

    # 转换为灰度
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 边缘检测
    edges = cv2.Canny(gray, 50, 150, apertureSize=3)

    # 查找轮廓
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="{width}" height="{height}" viewBox="0 0 {width} {height}">
  <defs>
    <style>
      .trace-path {{
        fill: none;
        stroke: #000000;
        stroke-width: 2;
        stroke-linecap: round;
        stroke-linejoin: round;
      }}
    </style>
  </defs>
  <rect width="{width}" height="{height}" fill="white"/>
'''

    # 处理轮廓
    for contour in contours:
        if cv2.contourArea(contour) > 20:
            # 简化轮廓
            epsilon = 0.01 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)

            if len(approx) >= 2:
                path_data = f"M {approx[0][0][0]} {approx[0][0][1]}"
                for j in range(1, len(approx)):
                    point = approx[j][0]
                    path_data += f" L {point[0]} {point[1]}"

                svg_content += f'  <path class="trace-path" d="{path_data}"/>\n'

    svg_content += '</svg>'

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(svg_content)

    print(f"描边SVG文件已保存: {output_path}")

def main():
    """主函数"""
    print("开始Logo矢量化处理 - 改进版...")

    # 检查输入文件
    logo_files = ['logo.jpg', 'logo1.jpg']

    for logo_file in logo_files:
        if not os.path.exists(logo_file):
            print(f"警告: 文件 {logo_file} 不存在，跳过处理")
            continue

        print(f"\n处理文件: {logo_file}")

        # 生成输出文件名
        base_name = os.path.splitext(logo_file)[0]
        simple_svg = f"{base_name}_optimized.svg"
        color_svg = f"{base_name}_color.svg"
        trace_svg = f"{base_name}_trace.svg"

        # 预处理图像
        processed_img, original_img = preprocess_image_for_vectorization(logo_file)
        if processed_img is not None:
            # 创建优化的黑白SVG
            create_optimized_svg(processed_img, original_img, simple_svg)

            # 创建彩色SVG
            create_color_svg_with_posterization(logo_file, color_svg)

            # 创建描边SVG
            create_trace_svg(logo_file, trace_svg)

        print(f"完成处理: {logo_file}")

    print("\n所有Logo矢量化处理完成！")
    print("生成的文件:")
    for logo_file in logo_files:
        if os.path.exists(logo_file):
            base_name = os.path.splitext(logo_file)[0]
            print(f"  - {base_name}_optimized.svg (优化黑白版本)")
            print(f"  - {base_name}_color.svg (彩色版本)")
            print(f"  - {base_name}_trace.svg (描边版本)")

if __name__ == "__main__":
    main()
