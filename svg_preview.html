<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logo SVG 预览</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .logo-section {
            margin-bottom: 40px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
        }
        .logo-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #444;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .logo-row {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        .logo-item {
            flex: 1;
            min-width: 300px;
            text-align: center;
        }
        .logo-item h3 {
            margin-bottom: 10px;
            color: #666;
        }
        .svg-container {
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 10px;
            background: white;
            display: inline-block;
            max-width: 100%;
        }
        .svg-container svg {
            max-width: 250px;
            max-height: 250px;
            width: auto;
            height: auto;
        }
        .download-links {
            margin-top: 15px;
        }
        .download-links a {
            display: inline-block;
            margin: 5px;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
        }
        .download-links a:hover {
            background: #0056b3;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .info h3 {
            margin-top: 0;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Logo SVG 矢量化结果</h1>
        
        <div class="info">
            <h3>矢量化说明</h3>
            <p>已成功将您的logo图片转换为SVG格式。每个logo都生成了两个版本：</p>
            <ul>
                <li><strong>Simple版本</strong>：黑白轮廓版本，适合简单的应用场景</li>
                <li><strong>Advanced版本</strong>：保留颜色信息的版本，更接近原始图片效果</li>
            </ul>
            <p>SVG格式的优势：可无限缩放、文件小、支持CSS样式修改、适合网页和印刷使用。</p>
        </div>

        <!-- Logo 1 -->
        <div class="logo-section">
            <div class="logo-title">Logo 1 (logo.jpg)</div>
            <div class="logo-row">
                <div class="logo-item">
                    <h3>简单版本 (黑白轮廓)</h3>
                    <div class="svg-container">
                        <object data="logo_simple.svg" type="image/svg+xml" width="250" height="250">
                            <img src="logo.jpg" alt="Logo 1 Simple" style="max-width: 250px; max-height: 250px;">
                        </object>
                    </div>
                    <div class="download-links">
                        <a href="logo_simple.svg" download>下载 Simple SVG</a>
                    </div>
                </div>
                <div class="logo-item">
                    <h3>高级版本 (保留颜色)</h3>
                    <div class="svg-container">
                        <object data="logo_advanced.svg" type="image/svg+xml" width="250" height="250">
                            <img src="logo.jpg" alt="Logo 1 Advanced" style="max-width: 250px; max-height: 250px;">
                        </object>
                    </div>
                    <div class="download-links">
                        <a href="logo_advanced.svg" download>下载 Advanced SVG</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Logo 2 -->
        <div class="logo-section">
            <div class="logo-title">Logo 2 (logo1.jpg)</div>
            <div class="logo-row">
                <div class="logo-item">
                    <h3>简单版本 (黑白轮廓)</h3>
                    <div class="svg-container">
                        <object data="logo1_simple.svg" type="image/svg+xml" width="250" height="250">
                            <img src="logo1.jpg" alt="Logo 2 Simple" style="max-width: 250px; max-height: 250px;">
                        </object>
                    </div>
                    <div class="download-links">
                        <a href="logo1_simple.svg" download>下载 Simple SVG</a>
                    </div>
                </div>
                <div class="logo-item">
                    <h3>高级版本 (保留颜色)</h3>
                    <div class="svg-container">
                        <object data="logo1_advanced.svg" type="image/svg+xml" width="250" height="250">
                            <img src="logo1.jpg" alt="Logo 2 Advanced" style="max-width: 250px; max-height: 250px;">
                        </object>
                    </div>
                    <div class="download-links">
                        <a href="logo1_advanced.svg" download>下载 Advanced SVG</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="info">
            <h3>使用建议</h3>
            <ul>
                <li>网页使用：可以直接在HTML中使用 &lt;img&gt; 或 &lt;object&gt; 标签引用SVG文件</li>
                <li>CSS样式：SVG支持CSS样式，可以动态改变颜色、大小等属性</li>
                <li>印刷用途：SVG是矢量格式，可以无损缩放到任意大小</li>
                <li>进一步优化：如需要更精细的效果，建议使用专业矢量图形软件如Adobe Illustrator进行手工调整</li>
            </ul>
        </div>
    </div>
</body>
</html>
