import cv2
import cv2.typing
import typing


# Classes
class ClassWithKeywordProperties:
    lambda_: int
    @property
    def except_(self) -> int: ...

    # Functions
    def __init__(self, lambda_arg: int = ..., except_arg: int = ...) -> None: ...



# Functions
@typing.overload
def copyMatAndDumpNamedArguments(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., lambda_: int = ..., sigma: float = ...) -> tuple[str, cv2.typing.MatLike]: ...
@typing.overload
def copyMatAndDumpNamedArguments(src: cv2.UMat, dst: cv2.UMat | None = ..., lambda_: int = ..., sigma: float = ...) -> tuple[str, cv2.UMat]: ...

def dumpBool(argument: bool) -> str: ...

def dumpCString(argument: str) -> str: ...

def dumpDouble(argument: float) -> str: ...

def dumpFloat(argument: float) -> str: ...

@typing.overload
def dumpInputArray(argument: cv2.typing.MatLike) -> str: ...
@typing.overload
def dumpInputArray(argument: cv2.UMat) -> str: ...

@typing.overload
def dumpInputArrayOfArrays(argument: typing.Sequence[cv2.typing.MatLike]) -> str: ...
@typing.overload
def dumpInputArrayOfArrays(argument: typing.Sequence[cv2.UMat]) -> str: ...

@typing.overload
def dumpInputOutputArray(argument: cv2.typing.MatLike) -> tuple[str, cv2.typing.MatLike]: ...
@typing.overload
def dumpInputOutputArray(argument: cv2.UMat) -> tuple[str, cv2.UMat]: ...

@typing.overload
def dumpInputOutputArrayOfArrays(argument: typing.Sequence[cv2.typing.MatLike]) -> tuple[str, typing.Sequence[cv2.typing.MatLike]]: ...
@typing.overload
def dumpInputOutputArrayOfArrays(argument: typing.Sequence[cv2.UMat]) -> tuple[str, typing.Sequence[cv2.UMat]]: ...

def dumpInt(argument: int) -> str: ...

def dumpInt64(argument: int) -> str: ...

def dumpRange(argument: cv2.typing.Range) -> str: ...

def dumpRect(argument: cv2.typing.Rect) -> str: ...

def dumpRotatedRect(argument: cv2.typing.RotatedRect) -> str: ...

def dumpSizeT(argument: int) -> str: ...

def dumpString(argument: str) -> str: ...

def dumpTermCriteria(argument: cv2.typing.TermCriteria) -> str: ...

def dumpVec2i(value: cv2.typing.Vec2i = ...) -> str: ...

def dumpVectorOfDouble(vec: typing.Sequence[float]) -> str: ...

def dumpVectorOfInt(vec: typing.Sequence[int]) -> str: ...

def dumpVectorOfRect(vec: typing.Sequence[cv2.typing.Rect]) -> str: ...

def generateVectorOfInt(len: int) -> typing.Sequence[int]: ...

def generateVectorOfMat(len: int, rows: int, cols: int, dtype: int, vec: typing.Sequence[cv2.typing.MatLike] | None = ...) -> typing.Sequence[cv2.typing.MatLike]: ...

def generateVectorOfRect(len: int) -> typing.Sequence[cv2.typing.Rect]: ...

@typing.overload
def testAsyncArray(argument: cv2.typing.MatLike) -> cv2.AsyncArray: ...
@typing.overload
def testAsyncArray(argument: cv2.UMat) -> cv2.AsyncArray: ...

def testAsyncException() -> cv2.AsyncArray: ...

@typing.overload
def testOverloadResolution(value: int, point: cv2.typing.Point = ...) -> str: ...
@typing.overload
def testOverloadResolution(rect: cv2.typing.Rect) -> str: ...

def testOverwriteNativeMethod(argument: int) -> int: ...

def testRaiseGeneralException() -> None: ...

def testReservedKeywordConversion(positional_argument: int, lambda_: int = ..., from_: int = ...) -> str: ...

def testRotatedRect(x: float, y: float, w: float, h: float, angle: float) -> cv2.typing.RotatedRect: ...

def testRotatedRectVector(x: float, y: float, w: float, h: float, angle: float) -> typing.Sequence[cv2.typing.RotatedRect]: ...


